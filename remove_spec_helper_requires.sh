#!/bin/bash

# <PERSON>ript to remove require "spec_helper" or require 'spec_helper' lines from all files
# under /workspace/sentry-ruby/sentry-ruby/spec directory without leaving empty lines

set -e

SPEC_DIR="/workspace/sentry-ruby/sentry-ruby/spec"

# Check if the directory exists
if [ ! -d "$SPEC_DIR" ]; then
    echo "Error: Directory $SPEC_DIR does not exist"
    exit 1
fi

echo "Removing require 'spec_helper' and require \"spec_helper\" lines from files in $SPEC_DIR..."

# Find all .rb files and process them
find "$SPEC_DIR" -name "*.rb" -type f | while read -r file; do
    echo "Processing: $file"

    # Use grep -v to exclude lines and write to temp file, then move back
    # This approach is more reliable for not leaving empty lines
    temp_file=$(mktemp)
    grep -v -E '^[[:space:]]*require[[:space:]]+['\''"]spec_helper['\''"][[:space:]]*$' "$file" > "$temp_file"
    mv "$temp_file" "$file"
done

echo "Done! All require 'spec_helper' and require \"spec_helper\" lines have been removed."
