services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    volumes:
      - ..:/workspace/sentry-ruby:cached
    command: sleep infinity
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    depends_on:
      - redis

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"
