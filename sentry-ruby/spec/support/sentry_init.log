2025-02-17 12:15:09 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:15:09 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:15:10 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:19:20 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:19:20 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:19:20 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:20:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:20:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:20:51 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:27:09 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:27:09 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:27:10 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:30:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:30:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:30:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:33:38 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:33:38 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:33:39 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:34:58 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:34:58 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:34:58 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:35:25 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:35:25 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:35:25 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:36:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:36:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:36:03 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:40:11 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:40:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:40:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:41:53 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:41:53 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:41:53 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:42:49 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:42:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:42:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:43:14 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:43:15 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:43:15 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:47:11 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:47:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:47:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:02 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:03 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:37 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:37 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:37 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:54 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:51:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:31 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:31 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:32 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:58 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:59 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:52:59 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:54:31 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:54:32 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:54:32 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:55:49 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:55:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:55:50 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:10 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:10 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:10 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:40 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:40 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 12:56:40 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:00:06 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:00:06 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:00:06 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:01:07 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:01:08 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:01:08 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:38 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:38 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:39 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:56 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:57 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:02:57 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:04:47 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:04:47 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:04:48 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:05:13 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:05:13 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:05:13 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:06:15 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:06:15 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:06:15 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:12:41 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:12:41 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:12:41 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:12 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:54 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:54 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:13:54 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:16:42 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:16:42 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:16:43 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:17:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:17:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:17:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:18:55 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:18:56 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

2025-02-17 13:18:56 - Sentry.init called from:
/workspaces/sentry/sentry-ruby/sentry-ruby/spec/support/rakefile.rb:6:in `<top (required)>'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/rake_module.rb:29:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:703:in `raw_load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:104:in `block in load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:103:in `load_rakefile'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:82:in `block in run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:186:in `standard_exception_handling'
/usr/local/rvm/gems/default/gems/rake-12.3.3/lib/rake/application.rb:80:in `run'
/usr/local/rvm/gems/default/gems/rake-12.3.3/exe/rake:27:in `<top (required)>'
/usr/local/rvm/gems/default/bin/rake:25:in `load'
/usr/local/rvm/gems/default/bin/rake:25:in `<top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/lib/ruby/3.3.0/bundler/cli/exec.rb:23:in `run'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:455:in `exec'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/command.rb:28:in `run'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor.rb:527:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:35:in `dispatch'
/usr/local/lib/ruby/3.3.0/bundler/vendor/thor/lib/thor/base.rb:584:in `start'
/usr/local/lib/ruby/3.3.0/bundler/cli.rb:29:in `start'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:28:in `block in <top (required)>'
/usr/local/lib/ruby/3.3.0/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/lib/ruby/gems/3.3.0/gems/bundler-2.5.11/exe/bundle:20:in `<top (required)>'
/usr/local/rvm/gems/default/bin/bundle:25:in `load'
/usr/local/rvm/gems/default/bin/bundle:25:in `<main>'

