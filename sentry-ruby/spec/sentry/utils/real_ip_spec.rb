# frozen_string_literal: true


RSpec.describe Sentry::Utils::RealIp do
  context "when no ip addresses are provided other than REMOTE_ADDR" do
    subject { Sentry::Utils::RealIp.new(remote_addr: "*******") }

    it "should return the remote_addr" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "when a list of x-forwarded-for ips is provided" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******, *******, *******, ***********",
        remote_addr: "***********"
      )
    end

    it "should return the oldest ancestor that is not a local IP" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "when client/real ips are provided" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******",
        real_ip: "*******",
        client_ip: "*******",
        remote_addr: "***********"
      )
    end

    it "should return the oldest ancestor, preferring client/real ips first" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "all provided ip addresses are actually local addresses" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "127.0.0.1, ::1, 10.0.0.0",
        remote_addr: "***********"
      )
    end

    it "should return REMOTE_ADDR" do
      expect(subject.calculate_ip).to eq("***********")
    end
  end

  context "when custom proxies are provided" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******, *******, *******",
        trusted_proxies: ["*******"]
      )
    end

    it "should return the first IP not in the trusted proxy list" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "when custom proxies are provided as IPAddr" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******, *******, *******",
        trusted_proxies: [IPAddr.new("*******")]
      )
    end

    it "should return the first IP not in the trusted proxy list" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "when an invalid IP is provided" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******.4, *******",
        remote_addr: "***********"
      )
    end

    it "return the eldest valid IP" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end

  context "with IPv6 ips" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "2001:db8:a0b:12f0::1",
        remote_addr: "***********"
      )
    end

    it "return the eldest valid IP" do
      expect(subject.calculate_ip).to eq("2001:db8:a0b:12f0::1")
    end
  end

  context "when custom proxies are provided as IPAddr as IP subnet" do
    subject do
      Sentry::Utils::RealIp.new(
        forwarded_for: "*******, *******, *******",
        trusted_proxies: [IPAddr.new("*******/24")]
      )
    end

    it "should return the first IP not in the trusted proxy list" do
      expect(subject.calculate_ip).to eq("*******")
    end
  end
end
